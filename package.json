{"name": "woocommerce-shipping-australia-post", "title": "WooCommerce Shipping Australia Post", "version": "2.7.5", "homepage": "https://woocommerce.com/products/australia-post-shipping-method/", "repository": {"type": "git", "url": "git://github.com/woocommerce/woocommerce-shipping-australia-post.git"}, "devDependencies": {"node-wp-i18n": "~1.2.7"}, "config": {"use_pnpm": true, "translate": true, "use_gh_release_notes": true}, "scripts": {"prebuild": "rm -rf ./vendor && composer clear-cache", "build": "composer install --no-dev -o && pnpm makepot && pnpm archive", "build:dev": "composer install -o && pnpm makepot", "archive": "composer archive --file=$npm_package_name --format=zip", "postarchive": "rm -rf $npm_package_name && unzip $npm_package_name.zip -d $npm_package_name && rm $npm_package_name.zip && zip -r $npm_package_name.zip $npm_package_name && rm -rf $npm_package_name", "makepot": "wpi18n makepot --domain-path languages --pot-file $npm_package_name.pot --type plugin --main-file $npm_package_name.php --exclude node_modules,tests,docs", "build:qit": "pnpm run build && pnpm run archive:qit && pnpm run postarchive", "archive:qit": "composer archive --file=$npm_package_name --format=zip && pnpm run zip:phpstan_config", "zip:phpstan_config": "zip -r $npm_package_name.zip .phpstan/dist/* -j"}, "engines": {"node": "^22.14.0", "pnpm": "^10.4.1"}}