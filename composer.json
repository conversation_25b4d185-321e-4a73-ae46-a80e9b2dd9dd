{"name": "woocommerce/woocommerce-shipping-australia-post", "description": "Obtain parcel shipping rates dynamically via the Australia Post API for your orders.", "homepage": "https://woocommerce.com/products/australia-post-shipping-method/", "type": "wordpress-plugin", "license": "GPL-2.0+", "repositories": [{"type": "vcs", "url": "https://github.com/woocommerce/box-packer"}], "archive": {"exclude": ["!/assets", "!/languages", "/vendor/dvdoug/boxpacker", "!/vendor/dvdoug/boxpacker/src", "!/vendor/dvdoug/boxpacker/features/bootstrap", "/vendor/woocommerce/box-packer", "!/vendor/woocommerce/box-packer/src", "node_modules", "README.md", "phpcs.xml", "composer.json", "composer.lock", "package.json", "package-lock.json", "composer.json", "composer.lock", "pnpm-lock.yaml", "woocommerce-shipping-australia-post.zip", ".*"]}, "require": {"php": ">=7.4", "woocommerce/box-packer": "1.2.0", "automattic/jetpack-autoloader": "^3"}, "require-dev": {"wp-coding-standards/wpcs": "*", "dealerdirect/phpcodesniffer-composer-installer": "*", "woocommerce/qit-cli": "*", "woocommerce/woocommerce-sniffs": "*", "phpstan/phpstan": "^2", "szepeviktor/phpstan-wordpress": "^2", "php-stubs/wp-cli-stubs": "*", "lucasbustamante/stubz": "^0"}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "automattic/jetpack-autoloader": true}}, "scripts": {"phpstan": ["php -d memory_limit=2G ./vendor/bin/phpstan analyse --configuration=.phpstan/local-config.neon --level=2"], "check-security": ["./vendor/bin/phpcs . --ignore=vendor,.git,.phpstan,assets,node_modules --standard=./.phpcs.security.xml  --report-full --report-summary"], "check-php": ["./vendor/bin/phpcs . --ignore=vendor,.git,.phpstan,assets,node_modules --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra  --report-full --report-summary --colors"], "check-php:fix": ["./vendor/bin/phpcbf . --ignore=vendor,.git,.phpstan,assets,node_modules --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra  --report-full --report-summary --colors"], "check-all": ["./vendor/bin/phpcs . --ignore=vendor,.git,.phpstan,assets,node_modules --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra,WordPress-Docs  --report-full --report-summary --colors"], "check-all:fix": ["./vendor/bin/phpcbf . --ignore=vendor,.git,.phpstan,assets,node_modules --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra,WordPress-Docs  --report-full --report-summary --colors"], "qit:security": ["npm run build && composer install && ./vendor/bin/qit run:security woocommerce-shipping-australia-post --zip=woocommerce-shipping-australia-post.zip"]}}