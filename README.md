[![CI](https://github.com/woocommerce/woocommerce-shipping-australia-post/actions/workflows/merge_to_trunk.yml/badge.svg)](https://github.com/woocommerce/woocommerce-shipping-australia-post/actions/workflows/merge_to_trunk.yml)
[![CI](https://github.com/woocommerce/woocommerce-shipping-australia-post/actions/workflows/cron_qit.yml/badge.svg)](https://github.com/woocommerce/woocommerce-shipping-australia-post/actions/workflows/cron_qit.yml)

woocommerce-shipping-australia-post
====================

Obtain parcel shipping rates dynamically via the Australia Post API for your orders.

| Product Page | Documentation | Ideas board | Build Status |
| ------------ | ------------- | ----------- | ------------ |
| https://woocommerce.com/products/australia-post-shipping-method/ | https://docs.woocommerce.com/document/australia-post/ | https://ideas.woocommerce.com/forums/133476-woocommerce/category/75738-category-shipping-methods | [![Build Status](https://travis-ci.com/woocommerce/woocommerce-shipping-australia-post.svg?token=pB9vx6zyNSauMrAK15Js&branch=master)](https://travis-ci.com/woocommerce/woocommerce-shipping-australia-post) |

## NPM Scripts

WooCommerce Shipping Australia Post utilizes npm scripts for task management utilities.

`pnpm run build` - Runs the tasks necessary for a release. These may include building JavaScript, SASS, CSS minification, and language files.
