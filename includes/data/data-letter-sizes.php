<?php
/**
 * Letter Sizes.
 *
 * @package WC_Shipping_Australia_Post
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

return array(
	'AUS_LETTER_SIZE_DL'                  => array(
		'name'   => 'DL',
		'width'  => 110,
		'length' => 220,
	),
	'AUS_LETTER_SIZE_C6'                  => array(
		'name'   => 'C6',
		'width'  => 114,
		'length' => 162,
	),
	'AUS_LETTER_SIZE_C5'                  => array(
		'name'   => 'C5',
		'width'  => 162,
		'length' => 229,
	),
	'AUS_LETTER_SIZE_C4'                  => array(
		'name'   => 'C4',
		'width'  => 229,
		'length' => 324,
	),
	'AUS_LETTER_SIZE_B4'                  => array(
		'name'   => 'B4',
		'width'  => 250,
		'length' => 353,
	),
	'INT_LETTER_COR_OWN_PACKAGING'        => array(
		'name'      => 'Courier',
		'width'     => 260,
		'length'    => 360,
		'thickness' => 20,
	),
	'INT_LETTER_EXP_OWN_PACKAGING'        => array(
		'name'      => 'Express',
		'width'     => 260,
		'length'    => 360,
		'thickness' => 20,
	),
	'INT_LETTER_REG_SMALL_ENVELOPE'       => array(
		'name'   => 'Registered Post DL',
		'width'  => 130,
		'length' => 240,
	),
	'INT_LETTER_REG_LARGE_ENVELOPE'       => array(
		'name'   => 'Registered Post B4',
		'width'  => 250,
		'length' => 355,
	),
	'INT_LETTER_AIR_OWN_PACKAGING_MEDIUM' => array(
		'name'      => 'Economy Air',
		'width'     => 130,
		'length'    => 240,
		'thickness' => 5,
	),
	'INT_LETTER_AIR_OWN_PACKAGING_HEAVY'  => array(
		'name'      => 'Economy Air',
		'width'     => 260,
		'length'    => 360,
		'thickness' => 20,
	),
);
