<?php
/**
 * Australia Post Privacy.
 *
 * @package WC_Shipping_Australia_Post
 */

if ( ! class_exists( 'WC_Abstract_Privacy' ) ) {
	return;
}

/**
 * WC_Australia_Post_Privacy class.
 */
class WC_Australia_Post_Privacy extends WC_Abstract_Privacy {
	/**
	 * Constructor
	 */
	public function __construct() {
		parent::__construct( __( 'Australia Post', 'woocommerce-shipping-australia-post' ) );
	}

	/**
	 * Gets the message of the privacy to display.
	 */
	public function get_privacy_message(): string {
		// translators: %s is privacy page link.
		return wpautop( sprintf( __( 'By using this extension, you may be storing personal data or sharing data with an external service. <a href="%s" target="_blank">Learn more about how this works, including what you may want to include in your privacy policy.</a>', 'woocommerce-shipping-australia-post' ), 'https://docs.woocommerce.com/document/privacy-shipping/#woocommerce-shipping-australia-post' ) );
	}
}

new WC_Australia_Post_Privacy();
